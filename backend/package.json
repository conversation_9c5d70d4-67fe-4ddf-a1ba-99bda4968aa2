{"name": "sh-ui-config-backend", "version": "1.0.0", "description": "Simple backend to provide secure configuration to frontend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"@google-cloud/translate": "^8.0.2", "@supabase/supabase-js": "^2.52.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2"}, "devDependencies": {"nodemon": "^3.0.2"}}