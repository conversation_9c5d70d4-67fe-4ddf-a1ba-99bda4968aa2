INSERT INTO "public"."cities" ("id", "name", "state", "country", "image_url", "description", "population", "avg_rent", "is_featured", "created_at", "updated_at") VALUES ('1', 'New York', 'NY', 'United States', 'https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8
  fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80', 'The city that never sleeps, offering endless opportunities for students and young 
  professionals.', '8336817', '2800', 'true', '2025-07-06 06:56:59.314402+00', '2025-07-06 06:56:59.314402+00'), ('2', 'San Francisco', 'CA', 'United States', 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGV
  ufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80', 'Tech hub with stunning bay views and vibrant neighborhoods.', '873965', '3200', 'true', '2025-07-06 06:56:59.314402+00', '2025-07-06 06:56:59.314402+00'), ('3', 'Boston', 'MA', 'United States', 'https://images.unsplash.com/photo-1598977123118-4e30ba3c4f5b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fH
  x8fA%3D%3D&auto=format&fit=crop&w=2340&q=80', 'Historic city with world-class universities and rich academic culture.', '695506', '2400', 'true', '2025-07-06 06:56:59.314402+00', '2025-07-06 06:56:59.314402+00'), ('4', 'Washington', 'DC', 'United States', 'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufD
  B8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80', 'The nations capital with government opportunities and cultural attractions.', '705749', '2200', 'true', '2025-07-06 06:56:59.314402+00', '2025-07-06 06:56:59.314402+00'), ('5', 'Chicago', 'IL', 'United States', 'https://images.unsplash.com/photo-1477414348463-c0eb7f1359b6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8f
  Hx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80', 'Midwestern metropolis known for architecture, food, and lakefront living.', '2693976', '1800', 'true', '2025-07-06 06:56:59.314402+00', '2025-07-06 06:56:59.314402+00'), ('6', 'Los Angeles', 'CA', 'United States', 'https://static.independent.co.uk/2024/09/26/15/iStock-1463288473-1.jpg', 'Entertainment capital with year-round sunshine and diverse neighborhoods.', '3971883', '2600', 'true', '2025-07-06 06:56:59.314402+00', '2025-07-06 06:56:59.314402+00'), ('7', 'Seattle', 'WA', 'United States', 'https://uploads.visitseattle.org/2023/05/09125333/RachaelJones_Skyline-Banner_2.jpg', 'Pacific Northwest gem with tech companies and outdoor recreation.', '753675', '2300', 'true', '2025-07-06 06:56:59.314402+00', '2025-07-06 06:56:59.314402+00'), ('8', 'Philadelphia', 'PA', 'United States', 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSNtPYZ0dLSABP2Lz-kjO3gHYfcXc9UpqTbBg&s', 'City of brotherly love with rich history and affordable living.', '1584064', '1600', 'true', '2025-07-06 06:56:59.314402+00', '2025-07-06 06:56:59.314402+00');