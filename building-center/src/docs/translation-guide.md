# Moveasy 多语言翻译使用指南

## 概述

本文档介绍如何在Moveasy平台中使用多语言翻译功能。我们的翻译系统基于谷歌翻译API，支持简体中文、繁体中文和英文等多种语言。

## 支持的语言

目前支持以下语言：
- 英文 (en)
- 简体中文 (zh)
- 繁体中文 (zh-TW)
- 日语 (ja)
- 韩语 (ko)
- 法语 (fr)
- 德语 (de)
- 西班牙语 (es)
- 俄语 (ru)
- 阿拉伯语 (ar)

## 使用方式

### 1. 基础文本翻译组件

对于需要翻译的静态文本，直接使用 `TranslatedText` 组件：

```vue
<template>
  <div>
    <h1><translated-text text="Welcome to Moveasy" /></h1>
    <p><translated-text text="Find your perfect home away from home" /></p>
  </div>
</template>

<script setup>
import TranslatedText from '@/components/TranslatedText.vue';
</script>
```

#### 组件属性

- `text` (必需): 要翻译的文本
- `from` (可选): 源语言代码，默认为"en"
- `to` (可选): 目标语言代码，默认为当前选择的语言
- `async` (可选): 是否异步翻译，适用于长文本，默认为false
- `tag` (可选): 异步模式下使用的HTML标签，默认为"div"

### 2. 翻译组合API

在组件内使用翻译功能：

```vue
<script setup>
import { useTranslation } from '@/composables/useTranslation';

// 获取翻译服务
const { translate, currentLanguage } = useTranslation({ createContext: false });

// 使用翻译函数
const message = ref('');
const translatedMessage = ref('');

async function translateMessage() {
  translatedMessage.value = await translate(message.value);
}
</script>
```

### 3. 批量翻译

对于需要批量翻译的文本：

```vue
<script setup>
import { useTranslation } from '@/composables/useTranslation';

const { translateBatch } = useTranslation({ createContext: false });

const texts = ['Hello', 'World', 'Welcome'];
const translatedTexts = await translateBatch(texts);
</script>
```

## 翻译策略实践指南

### 什么需要翻译，什么不需要翻译？

#### 需要翻译的内容：
- 显示给用户的界面文本和消息
- 帮助文档和提示
- 错误消息
- 数据库中的多语言内容字段

#### 不需要翻译的内容：
- 代码变量名、函数名
- 数据库字段名
- 用户输入的数据（除非明确需要）
- 项目内部文档

### 组件翻译策略

我们采用"按需翻译"的策略，而不是为每个组件都添加翻译。具体如下：

#### 父子组件关系中的翻译

1. **父组件传递已翻译的内容给子组件**
   - 父组件负责翻译，子组件只负责显示
   - 这种方式适合父组件对子组件有明确控制的场景

   ```vue
   <!-- 父组件 -->
   <template>
     <child-component :title="translatedTitle" />
   </template>
   
   <script setup>
   import { ref, onMounted } from 'vue';
   import { useTranslation } from '@/composables/useTranslation';
   
   const { translate } = useTranslation();
   const translatedTitle = ref('');
   
   onMounted(async () => {
     translatedTitle.value = await translate('Welcome');
   });
   </script>
   ```

2. **子组件内部使用翻译上下文**
   - 适合子组件需要自主决定翻译内容的场景
   - 子组件通过 `inject` 获取上层的翻译上下文

   ```vue
   <!-- 子组件 -->
   <template>
     <div>
       <h2>{{ translatedTitle }}</h2>
     </div>
   </template>
   
   <script setup>
   import { ref, onMounted } from 'vue';
   import { useTranslation, TRANSLATION_CONTEXT_KEY } from '@/composables/useTranslation';
   import { inject } from 'vue';
   
   // 尝试获取上层提供的翻译上下文
   const translationContext = inject(TRANSLATION_CONTEXT_KEY, null);
   // 如果没有上下文，创建新的翻译服务
   const { translate } = translationContext || useTranslation({ createContext: false });
   
   const translatedTitle = ref('');
   
   onMounted(async () => {
     translatedTitle.value = await translate('Section Title');
   });
   </script>
   ```

### 动态内容翻译

对于动态生成的内容（如API返回的数据），推荐使用以下策略：

1. **后端翻译**：优先在后端进行翻译，前端只负责显示
2. **前端按需翻译**：对于需要在前端动态翻译的内容，使用 `translate` 函数

```vue
<script setup>
import { ref, watch } from 'vue';
import { useTranslation } from '@/composables/useTranslation';

const { translate, currentLanguage } = useTranslation();
const apiData = ref(null);
const translatedData = ref(null);

// 当语言变化时重新翻译
watch([apiData, currentLanguage], async () => {
  if (apiData.value) {
    // 翻译标题
    const translatedTitle = await translate(apiData.value.title);
    // 翻译描述
    const translatedDescription = await translate(apiData.value.description);
    
    translatedData.value = {
      ...apiData.value,
      title: translatedTitle,
      description: translatedDescription
    };
  }
}, { immediate: true });
</script>
```

## 最佳实践

1. **使用语境翻译**：尽量提供完整的句子或短语进行翻译，而不是单词
2. **优化请求**：使用批量翻译API减少请求次数
3. **缓存翻译**：系统已内置三级缓存机制，无需手动缓存
4. **测试多语言**：定期切换语言测试界面布局是否正常
5. **避免硬编码文本**：不要在模板中硬编码文本，而是使用 `TranslatedText` 组件

## 故障排除

如果翻译无法正常工作，请检查：

1. 网络连接是否正常
2. 谷歌翻译API是否已配置正确
3. 查看浏览器控制台是否有错误信息
4. 检查语言代码是否正确

## 维护和更新

本翻译系统由开发团队维护。如需增加新的语言支持或发现翻译问题，请联系项目负责人。 