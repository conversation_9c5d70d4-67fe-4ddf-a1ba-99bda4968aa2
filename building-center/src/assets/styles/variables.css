:root {
  /* 主题色 - 绿色系主题 */
  --primary-color: #198754;       /* 主色调-绿色 Green-500 */
  --primary-hover: #146c43;       /* 主色调悬停-深绿色 Green-600 */
  --secondary-color: #47976f;     /* 次要色调-浅绿色 Green-400 */
  --accent-color: #75b798;        /* 强调色-更浅绿色 Green-300 */
  --background-color: #f5f5f5;    /* 背景色-浅灰色 */
  --text-primary: #333333;        /* 主要文本颜色 */
  --text-secondary: #666666;      /* 次要文本颜色 */
  --border-color: #dddddd;        /* 边框颜色 */
  --error-color: #e74c3c;         /* 错误提示色 */
  --success-color: #0f5132;       /* 成功提示色-深绿色 Green-700 */
  --success-bg: #d1e7dd;          /* 成功背景色-极浅绿色 Green-100 */
  --warning-color: #f39c12;       /* 警告提示色 */
  --highlight-bg: #e3f0eb;        /* 高亮背景色 Green-200 */

  /* 字体 */
  --font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  
  /* 尺寸 */
  --border-radius: 4px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 阴影 */
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
} 