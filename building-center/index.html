<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- 开发环境下使用宽松的CSP策略 -->
    <meta http-equiv="Content-Security-Policy" content="default-src * 'unsafe-inline' 'unsafe-eval' data: blob:;">
    <!-- 设置跨源隔离策略 -->
    <meta http-equiv="Cross-Origin-Opener-Policy" content="same-origin-allow-popups">
    <!-- 添加Referrer Policy -->
    <meta name="referrer" content="no-referrer-when-downgrade">
    <title>Moveasy</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
