# Node.js 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 包管理器锁文件（保留 package-lock.json，忽略其他）
yarn.lock
pnpm-lock.yaml

# 构建产物
dist/
build/
.nuxt/
.output/
.vite/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 临时文件
.tmp/
.cache/
.temp/

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# SSH 和用户配置文件
.ssh/
.bash_history
.bashrc
.profile
.pydistutils.cfg
.gitconfig

# PM2 进程管理器
.pm2/

# NPM 缓存
.npm/

# Cursor 编辑器
.cursor-server/

# Python 相关
.pip/
__pycache__/
*.py[cod]
*$py.class

# 压缩文件和备份
*.tar.gz
*.zip
*.rar
*.7z
*_backup_*.tar.gz

# 项目特定的临时文件
sh-ui-demo.tar.gz
building_center_backup_*.tar.gz

# 覆盖范围报告
coverage/
*.lcov

# nyc 测试覆盖
.nyc_output

# 依赖锁定文件
package-lock.json

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/ 